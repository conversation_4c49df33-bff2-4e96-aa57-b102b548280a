import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

type UserProfile = Tables<'user_profiles'>;
type Organization = Tables<'organizations'>;
type OrganizationMember = Tables<'organization_members'>;

interface AuthContextType {
  // Authentication state
  user: User | null;
  session: Session | null;
  userProfile: UserProfile | null;
  
  // Organization state
  currentOrganization: Organization | null;
  organizations: Organization[];
  userRole: string | null;
  
  // Loading states
  loading: boolean;
  profileLoading: boolean;
  organizationsLoading: boolean;
  
  // Authentication methods
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string, userData?: Partial<UserProfile>) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  
  // Organization methods
  switchOrganization: (organizationId: string) => Promise<void>;
  createOrganization: (name: string) => Promise<{ data: Organization | null; error: any }>;
  
  // Profile methods
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  
  // Utility methods
  hasPermission: (permission: string) => boolean;
  isOwner: () => boolean;
  isAdmin: () => boolean;
  isManager: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Authentication state
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Organization state
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Loading states
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [organizationsLoading, setOrganizationsLoading] = useState(false);

  // Prevent multiple initializations
  const initializationRef = React.useRef(false);
  const mountedRef = React.useRef(true);

  // Add timeout to force loading to complete (reduced timeout)
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading || profileLoading || organizationsLoading) {
        console.warn('⚠️ Loading timeout reached, forcing completion...');
        setLoading(false);
        setProfileLoading(false);
        setOrganizationsLoading(false);
      }
    }, 8000); // 8 second timeout (reduced from 15)

    return () => clearTimeout(timeout);
  }, [loading, profileLoading, organizationsLoading]);

  // Debug loading states
  useEffect(() => {
    console.log('📊 Loading states:', { loading, profileLoading, organizationsLoading });
  }, [loading, profileLoading, organizationsLoading]);

  // Initialize auth state
  useEffect(() => {
    // Prevent multiple initializations
    if (initializationRef.current) {
      console.log('⚠️ Auth context already initialized, skipping...');
      return;
    }

    console.log('🔐 Initializing auth context...');
    initializationRef.current = true;
    let isInitializing = true;

    // Get initial session
    supabase.auth.getSession().then(async ({ data: { session }, error }) => {
      if (!isInitializing || !mountedRef.current) {
        console.log('⚠️ Skipping initial session - component unmounted');
        return;
      }

      console.log('📱 Initial session:', session?.user?.id ? `User logged in: ${session.user.id}` : 'No user');

      if (error) {
        console.error('❌ Error getting initial session:', error);
        if (mountedRef.current) setLoading(false);
        return;
      }

      if (mountedRef.current) {
        setSession(session);
        setUser(session?.user ?? null);
      }

      if (session?.user && mountedRef.current) {
        console.log('🔄 Loading user data for initial session...');
        try {
          console.log('🔄 Starting profile and organizations loading...');
          // Load profile and organizations sequentially to avoid race conditions
          await loadUserProfile(session.user.id);
          await loadUserOrganizations(session.user.id);
          console.log('✅ Initial user data loaded successfully');
        } catch (error) {
          console.error('❌ Error loading user data:', error);
          // Ensure loading states are cleared even on error
          if (mountedRef.current) {
            setProfileLoading(false);
            setOrganizationsLoading(false);
          }
        }
      }

      // Set main loading to false immediately after data loading completes
      console.log('✅ Setting initial loading to false');
      if (mountedRef.current) setLoading(false);
    }).catch((error) => {
      console.error('❌ Error in getSession:', error);
      if (mountedRef.current) setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mountedRef.current) {
        console.log('⚠️ Component unmounted, ignoring auth state change');
        return;
      }

      console.log('🔄 Auth state changed:', event, session?.user?.id ? `User: ${session.user.id}` : 'No user');

      // Prevent processing during initial load
      if (isInitializing && event === 'INITIAL_SESSION') {
        console.log('⚠️ Skipping INITIAL_SESSION event - already processed');
        return;
      }

      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        console.log('🔄 Loading user data for auth state change...');
        try {
          // Load profile and organizations sequentially to avoid race conditions
          await loadUserProfile(session.user.id);
          await loadUserOrganizations(session.user.id);
          console.log('✅ Auth change user data loaded successfully');
        } catch (error) {
          console.error('❌ Error loading user data on auth change:', error);
          // Ensure loading states are cleared even on error
          if (mountedRef.current) {
            setProfileLoading(false);
            setOrganizationsLoading(false);
          }
        }
      } else {
        // Clear state on sign out
        console.log('🧹 Clearing user state on sign out');
        setUserProfile(null);
        setCurrentOrganization(null);
        setOrganizations([]);
        setUserRole(null);
        setProfileLoading(false);
        setOrganizationsLoading(false);
      }
      console.log('✅ Setting auth change loading to false');
      setLoading(false);
    });

    return () => {
      console.log('🧹 Cleaning up auth context');
      mountedRef.current = false;
      isInitializing = false;
      subscription.unsubscribe();
    };
  }, []);

  // Load user profile
  const loadUserProfile = async (userId: string) => {
    if (!mountedRef.current) {
      console.log('⚠️ Component unmounted, skipping profile load');
      return;
    }

    console.log('👤 Loading user profile for:', userId);
    setProfileLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (!mountedRef.current) return;

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Error loading user profile:', error);
        setUserProfile(null);
        return;
      }

      if (data) {
        console.log('✅ User profile loaded:', data);
        setUserProfile(data);
      } else {
        console.log('⚠️ No user profile found');
        setUserProfile(null);
      }
    } catch (error) {
      console.error('❌ Error loading user profile:', error);
      if (mountedRef.current) setUserProfile(null);
    } finally {
      console.log('✅ Profile loading complete, setting profileLoading to false');
      if (mountedRef.current) {
        setProfileLoading(false);
        console.log('📊 Profile loading state set to false');
      } else {
        console.log('⚠️ Component unmounted, skipping profileLoading reset');
      }
    }
  };

  // Load user organizations
  const loadUserOrganizations = async (userId: string) => {
    if (!mountedRef.current) {
      console.log('⚠️ Component unmounted, skipping organizations load');
      return;
    }

    console.log('🏢 Loading organizations for user:', userId);
    setOrganizationsLoading(true);
    try {
      const { data: memberships, error } = await supabase
        .from('organization_members')
        .select(`
          role,
          organization_id,
          organizations (*)
        `)
        .eq('user_id', userId)
        .eq('is_active', true);

      if (!mountedRef.current) return;

      if (error) {
        console.error('❌ Error loading organizations:', error);
        if (mountedRef.current) {
          setOrganizations([]);
          setCurrentOrganization(null);
          setUserRole(null);
        }
        // Don't return early - let finally block handle loading state
        console.log('📋 Raw memberships data:', memberships);

          if (memberships && memberships.length > 0) {
            const orgs = memberships.map((m: any) => m.organizations).filter(Boolean);
            console.log('🏢 Processed organizations:', orgs);
            if (mountedRef.current) setOrganizations(orgs);

            // Set current organization (first one or from localStorage)
            const savedOrgId = localStorage.getItem('currentOrganizationId');
            console.log('💾 Saved organization ID:', savedOrgId);

            const currentOrg = savedOrgId
              ? orgs.find((org: Organization) => org.id === savedOrgId)
              : orgs[0];

            if (currentOrg && mountedRef.current) {
              console.log('✅ Setting current organization:', currentOrg);
              setCurrentOrganization(currentOrg);
              const membership = memberships.find((m: any) => m.organization_id === currentOrg.id);
              console.log('👤 User role in organization:', membership?.role);
              setUserRole(membership?.role || null);
              localStorage.setItem('currentOrganizationId', currentOrg.id);
            } else {
              console.log('⚠️ No current organization found');
              if (mountedRef.current) {
                setCurrentOrganization(null);
                setUserRole(null);
              }
            }
          } else {
            console.log('⚠️ No memberships found');
            if (mountedRef.current) {
              setOrganizations([]);
              setCurrentOrganization(null);
              setUserRole(null);
            }
          }
        }
    } catch (error) {
      console.error('❌ Error loading organizations:', error);
      if (mountedRef.current) {
        setOrganizations([]);
        setCurrentOrganization(null);
        setUserRole(null);
      }
    } finally {
      console.log('✅ Organizations loading complete, setting organizationsLoading to false');
      if (mountedRef.current) {
        setOrganizationsLoading(false);
        console.log('📊 Organizations loading state set to false');
      } else {
        console.log('⚠️ Component unmounted, skipping organizationsLoading reset');
      }
    }
  };

  // Authentication methods
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, userData?: Partial<UserProfile>) => {
    console.log('🔐 Starting user signup:', { email, hasUserData: !!userData, userData });

    const signupData = userData ? {
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      phone: userData.phone || '',
      company_name: (userData as any).company_name || '',
      annual_revenue: (userData as any).annual_revenue || '',
      accounting_services: (userData as any).accounting_services || false,
      business_loan: (userData as any).business_loan || false,
    } : {};

    console.log('📝 Signup data being sent to Supabase:', signupData);

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: signupData
      }
    });

    if (error) {
      console.error('❌ Signup error:', error);
      return { error };
    }

    console.log('✅ User created in auth.users:', data.user?.id);

    // The trigger should automatically create the user profile, but let's add a fallback
    if (data.user && userData) {
      console.log('👤 Ensuring user profile exists...');

      // Wait a moment for the trigger to execute
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if profile was created by trigger
      const { data: existingProfile } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', data.user.id)
        .single();

      if (!existingProfile) {
        console.log('⚠️ Profile not created by trigger, creating manually...');
        // Create user profile manually as fallback
        const { error: profileError } = await supabase
          .from('user_profiles')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            ...userData,
          });

        if (profileError) {
          console.error('❌ Error creating user profile manually:', profileError);
        } else {
          console.log('✅ User profile created manually');
        }
      } else {
        console.log('✅ User profile created by trigger');
      }
    }

    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      localStorage.removeItem('currentOrganizationId');
    }
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error };
  };

  // Organization methods
  const switchOrganization = async (organizationId: string) => {
    const org = organizations.find(o => o.id === organizationId);
    if (org) {
      setCurrentOrganization(org);
      localStorage.setItem('currentOrganizationId', organizationId);
      
      // Update user role for this organization
      const { data: membership } = await supabase
        .from('organization_members')
        .select('role')
        .eq('user_id', user!.id)
        .eq('organization_id', organizationId)
        .eq('is_active', true)
        .single();
        
      setUserRole(membership?.role || null);
    }
  };

  const createOrganization = async (name: string) => {
    console.log('🏢 Creating organization:', { name, user: user?.id });

    if (!user) {
      console.error('❌ User not authenticated');
      return { data: null, error: 'User not authenticated' };
    }

    // Check current session and auth state
    const { data: sessionData } = await supabase.auth.getSession();
    console.log('🔐 Current session:', {
      hasSession: !!sessionData.session,
      userId: sessionData.session?.user?.id,
      isExpired: sessionData.session ? new Date(sessionData.session.expires_at!) < new Date() : 'no session'
    });

    // Test database connection and auth
    console.log('🧪 Testing database connection...');
    const { data: testData, error: testError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (testError) {
      console.error('❌ Database connection test failed:', testError);
      return { data: null, error: testError };
    }
    console.log('✅ Database connection test passed');

    console.log('📝 Inserting organization into database...');

    // Check if we have a valid access token and refresh if needed
    const { data: { session: currentSession } } = await supabase.auth.getSession();
    console.log('🔑 Access token info:', {
      hasAccessToken: !!currentSession?.access_token,
      tokenLength: currentSession?.access_token?.length,
      expiresAt: currentSession?.expires_at,
      isExpired: currentSession ? new Date(currentSession.expires_at!) < new Date() : 'no session'
    });

    // If token is expired or about to expire, refresh it
    if (currentSession && new Date(currentSession.expires_at!) < new Date(Date.now() + 60000)) {
      console.log('🔄 Token expired or expiring soon, refreshing...');
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        console.error('❌ Token refresh failed:', refreshError);
        return { data: null, error: refreshError };
      }
      console.log('✅ Token refreshed successfully');
    }

    const { data: org, error: orgError } = await supabase
      .from('organizations')
      .insert({
        name,
      })
      .select()
      .single();

    if (orgError) {
      console.error('❌ Organization creation failed:', orgError);
      return { data: null, error: orgError };
    }

    console.log('✅ Organization created successfully:', org);

    // Add user as owner
    console.log('👤 Adding user as organization owner...');
    const { error: memberError } = await supabase
      .from('organization_members')
      .insert({
        organization_id: org.id,
        user_id: user.id,
        role: 'owner',
      });

    if (memberError) {
      console.error('❌ Failed to add user as organization owner:', memberError);
      return { data: null, error: memberError };
    }

    console.log('✅ User added as organization owner successfully');

    // Reload organizations
    console.log('🔄 Reloading user organizations...');
    await loadUserOrganizations(user.id);

    console.log('🎉 Organization creation completed successfully');
    return { data: org, error: null };
  };

  // Profile methods
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: 'User not authenticated' };

    const { error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', user.id);

    if (!error) {
      setUserProfile(prev => prev ? { ...prev, ...updates } : null);
    }

    return { error };
  };

  // Utility methods
  const hasPermission = (permission: string): boolean => {
    if (!userRole) return false;
    
    const roleHierarchy = {
      owner: 4,
      admin: 3,
      manager: 2,
      user: 1,
    };

    const permissionLevels = {
      'read': 1,
      'write': 2,
      'manage': 3,
      'admin': 4,
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = permissionLevels[permission as keyof typeof permissionLevels] || 0;

    return userLevel >= requiredLevel;
  };

  const isOwner = () => userRole === 'owner';
  const isAdmin = () => userRole === 'admin' || userRole === 'owner';
  const isManager = () => ['manager', 'admin', 'owner'].includes(userRole || '');

  const value: AuthContextType = {
    // Authentication state
    user,
    session,
    userProfile,
    
    // Organization state
    currentOrganization,
    organizations,
    userRole,
    
    // Loading states
    loading,
    profileLoading,
    organizationsLoading,
    
    // Authentication methods
    signIn,
    signUp,
    signOut,
    resetPassword,
    
    // Organization methods
    switchOrganization,
    createOrganization,
    
    // Profile methods
    updateProfile,
    
    // Utility methods
    hasPermission,
    isOwner,
    isAdmin,
    isManager,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
