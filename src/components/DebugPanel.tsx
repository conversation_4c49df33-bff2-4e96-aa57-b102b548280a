import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const DebugPanel: React.FC = () => {
  const { user, userProfile, organizations, currentOrganization, userRole } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState<any>({});

  const runDatabaseTests = async () => {
    const results: any = {};

    try {
      // Test 1: Check current user session
      const { data: session } = await supabase.auth.getSession();
      results.session = {
        status: session.session ? 'Active' : 'No session',
        userId: session.session?.user?.id || 'None',
        email: session.session?.user?.email || 'None'
      };

      // Test 2: Test organizations table access
      try {
        const { data: orgsData, error: orgsError } = await supabase
          .from('organizations')
          .select('*')
          .limit(1);
        
        results.organizationsAccess = {
          status: orgsError ? 'Error' : 'Success',
          error: orgsError?.message || null,
          canRead: !orgsError
        };
      } catch (err) {
        results.organizationsAccess = {
          status: 'Error',
          error: err instanceof Error ? err.message : 'Unknown error',
          canRead: false
        };
      }

      // Test 3: Test organization_members table access
      try {
        const { data: membersData, error: membersError } = await supabase
          .from('organization_members')
          .select('*')
          .limit(1);
        
        results.organizationMembersAccess = {
          status: membersError ? 'Error' : 'Success',
          error: membersError?.message || null,
          canRead: !membersError
        };
      } catch (err) {
        results.organizationMembersAccess = {
          status: 'Error',
          error: err instanceof Error ? err.message : 'Unknown error',
          canRead: false
        };
      }

      // Test 4: Test user_profiles table access
      try {
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user?.id || '')
          .single();
        
        results.userProfileAccess = {
          status: profileError ? 'Error' : 'Success',
          error: profileError?.message || null,
          canRead: !profileError,
          profileExists: !!profileData
        };
      } catch (err) {
        results.userProfileAccess = {
          status: 'Error',
          error: err instanceof Error ? err.message : 'Unknown error',
          canRead: false,
          profileExists: false
        };
      }

      // Test 5: Test organization creation permissions
      try {
        const testOrgName = `Test Org ${Date.now()}`;
        const { data: testOrg, error: createError } = await supabase
          .from('organizations')
          .insert({ name: testOrgName })
          .select()
          .single();

        if (!createError && testOrg) {
          // Clean up test organization
          await supabase
            .from('organizations')
            .delete()
            .eq('id', testOrg.id);
          
          results.organizationCreation = {
            status: 'Success',
            canCreate: true,
            error: null
          };
        } else {
          results.organizationCreation = {
            status: 'Error',
            canCreate: false,
            error: createError?.message || 'Unknown error'
          };
        }
      } catch (err) {
        results.organizationCreation = {
          status: 'Error',
          canCreate: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        };
      }

      setTestResults(results);
    } catch (error) {
      console.error('Error running database tests:', error);
      setTestResults({ error: 'Failed to run tests' });
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
        variant="outline"
      >
        🐛 Debug
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            Debug Panel
            <Button onClick={() => setIsVisible(false)} variant="outline" size="sm">
              ✕
            </Button>
          </CardTitle>
          <CardDescription>
            Debug information for troubleshooting authentication and database issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Auth State */}
          <div>
            <h3 className="font-semibold mb-2">Authentication State</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
              {JSON.stringify({
                user: user ? { id: user.id, email: user.email } : null,
                userProfile: userProfile ? { 
                  id: userProfile.id, 
                  email: userProfile.email,
                  first_name: userProfile.first_name,
                  last_name: userProfile.last_name
                } : null,
                organizations: organizations?.length || 0,
                currentOrganization: currentOrganization ? {
                  id: currentOrganization.id,
                  name: currentOrganization.name
                } : null,
                userRole
              }, null, 2)}
            </pre>
          </div>

          {/* Database Tests */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold">Database Tests</h3>
              <Button onClick={runDatabaseTests} size="sm">
                Run Tests
              </Button>
            </div>
            {Object.keys(testResults).length > 0 && (
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            )}
          </div>

          {/* Environment Info */}
          <div>
            <h3 className="font-semibold mb-2">Environment</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
              {JSON.stringify({
                supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
                hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
                environment: import.meta.env.MODE
              }, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugPanel;
